# 激活功能使用说明

## 功能概述

本次修改为ActivationDialog弹出增加了判断条件：如果用户未激活，需要先HEAD检查 `http://106.75.225.184/verify.php` 是否有效，有效才弹出激活对话框。

## 修改内容

### 1. ActivationUtil.java 新增功能

- 新增 `checkActivationServerAvailable()` 方法，使用HEAD请求检查激活服务器是否可用
- 新增 `ServerCheckCallback` 接口用于异步回调

```java
/**
 * 检查激活服务器是否可用
 */
public static void checkActivationServerAvailable(ServerCheckCallback callback) {
    // 在后台线程中执行HEAD请求
    new Thread(() -> {
        try {
            OkHttpClient client = OkGoHelper.getDefaultClient();
            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(ACTIVATION_URL)
                    .head() // 使用HEAD方法
                    .build();
            
            Call call = client.newCall(request);
            Response response = call.execute();
            
            // 检查响应状态码，200-299范围内认为服务器可用
            boolean isAvailable = response.isSuccessful();
            callback.onResult(isAvailable);
            
        } catch (Exception e) {
            // 网络异常或其他错误，认为服务器不可用
            callback.onResult(false);
        }
    }).start();
}
```

### 2. HomeActivity.java 修改激活检查逻辑

修改 `checkActivationStatus()` 方法，增加服务器可用性检查：

```java
private void checkActivationStatus() {
    if (!ActivationUtil.isActivated()) {
        // 用户未激活，先检查激活服务器是否可用
        ActivationUtil.checkActivationServerAvailable(new ActivationUtil.ServerCheckCallback() {
            @Override
            public void onResult(boolean isAvailable) {
                runOnUiThread(() -> {
                    if (isAvailable) {
                        // 服务器可用，显示激活对话框
                        showActivationDialog();
                    } else {
                        // 服务器不可用，直接继续初始化（跳过激活检查）
                        initData();
                    }
                });
            }
        });
    } else {
        // 已激活，继续正常初始化
        initData();
    }
}
```

## 工作流程

1. 应用启动时调用 `checkActivationStatus()`
2. 如果用户已激活，直接继续初始化
3. 如果用户未激活：
   - 先发送HEAD请求到激活服务器检查可用性
   - 如果服务器可用（返回2xx状态码），显示激活对话框
   - 如果服务器不可用（网络错误或非2xx状态码），跳过激活检查，直接继续初始化

## 优势

- **网络友好**: 使用HEAD请求，不下载响应体，节省带宽
- **容错性强**: 服务器不可用时不阻塞应用启动
- **异步处理**: 不阻塞UI线程
- **向后兼容**: 不影响现有激活功能

## 测试建议

1. 测试服务器可用时的正常激活流程
2. 测试服务器不可用时的降级处理
3. 测试网络异常情况下的处理
4. 测试已激活用户的正常使用
