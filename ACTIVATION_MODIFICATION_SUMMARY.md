# ActivationDialog 弹出条件修改总结

## 需求
ActivationDialog 弹出增加判断条件，如果用户未激活，需要先HEAD检查 `http://106.75.225.184/verify.php` 是否有效，有效才弹出。

## 实现方案

### 1. 修改文件列表
- `app/src/main/java/com/github/tvbox/osc/util/ActivationUtil.java` - 新增服务器可用性检查功能
- `app/src/main/java/com/github/tvbox/osc/ui/activity/HomeActivity.java` - 修改激活检查逻辑

### 2. 核心功能实现

#### ActivationUtil.java 新增功能
```java
/**
 * 检查激活服务器是否可用
 */
public static void checkActivationServerAvailable(ServerCheckCallback callback) {
    // 在后台线程中执行HEAD请求
    new Thread(() -> {
        try {
            OkHttpClient client = OkGoHelper.getDefaultClient();
            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(ACTIVATION_URL)
                    .head() // 使用HEAD方法
                    .build();
            
            Call call = client.newCall(request);
            Response response = call.execute();
            
            // 检查响应状态码，200-299范围内认为服务器可用
            boolean isAvailable = response.isSuccessful();
            callback.onResult(isAvailable);
            
        } catch (Exception e) {
            // 网络异常或其他错误，认为服务器不可用
            callback.onResult(false);
        }
    }).start();
}

public interface ServerCheckCallback {
    void onResult(boolean isAvailable);
}
```

#### HomeActivity.java 修改激活检查逻辑
```java
private void checkActivationStatus() {
    if (!ActivationUtil.isActivated()) {
        // 用户未激活，先检查激活服务器是否可用
        ActivationUtil.checkActivationServerAvailable(new ActivationUtil.ServerCheckCallback() {
            @Override
            public void onResult(boolean isAvailable) {
                runOnUiThread(() -> {
                    if (isAvailable) {
                        // 服务器可用，显示激活对话框
                        showActivationDialog();
                    } else {
                        // 服务器不可用，直接继续初始化（跳过激活检查）
                        initData();
                    }
                });
            }
        });
    } else {
        // 已激活，继续正常初始化
        initData();
    }
}
```

## 工作流程

1. **应用启动** → 调用 `checkActivationStatus()`
2. **检查激活状态**:
   - 如果已激活 → 直接初始化应用
   - 如果未激活 → 继续下一步
3. **服务器可用性检查**:
   - 发送HEAD请求到 `http://106.75.225.184/verify.php`
   - 检查响应状态码
4. **根据检查结果处理**:
   - 服务器可用(2xx状态码) → 显示激活对话框
   - 服务器不可用(网络错误/非2xx状态码) → 跳过激活，直接初始化应用

## 技术特点

- **轻量级检查**: 使用HEAD请求，不下载响应体，节省带宽
- **异步处理**: 网络请求在后台线程执行，不阻塞UI
- **容错性强**: 网络异常时自动降级，不影响应用正常使用
- **向后兼容**: 不影响现有激活功能和已激活用户的使用

## 测试建议

1. **正常场景**: 服务器可用时的激活流程
2. **降级场景**: 服务器不可用时的跳过处理
3. **网络异常**: 无网络连接时的处理
4. **已激活用户**: 确保不受影响

## 文件变更摘要

- **新增**: `ActivationUtil.checkActivationServerAvailable()` 方法
- **新增**: `ActivationUtil.ServerCheckCallback` 接口  
- **修改**: `HomeActivity.checkActivationStatus()` 方法逻辑
- **导入**: 添加必要的OkHttp相关导入

修改完成，功能已实现并可以投入使用。
